<template>
  <div v-loading="loading" class="h-full">
    <el-scrollbar>
      <div class="p-5">
        <h2 class="flex flex-wrap cursor-pointer items-center text-[28px] font-bold" @click="onBack">
          <el-icon size="20px"><Back /></el-icon>
          <div class="ml-2 break-words">{{ baseInfo?.name }}&nbsp;{{ props.fieldId }}</div>
        </h2>
        <div class="mt-5">
          <el-descriptions>
            <el-descriptions-item label="变量名称">{{ baseInfo?.name }}</el-descriptions-item>
            <el-descriptions-item label="变量中文含义">{{ baseInfo?.chineseMeaning || '无' }}</el-descriptions-item>
            <el-descriptions-item label="变量定义">{{ baseInfo?.explanation || '无' }}</el-descriptions-item>
            <el-descriptions-item label="字段类型">{{ fieldTypeText(baseInfo?.valueType) }}</el-descriptions-item>
            <el-descriptions-item label="变量值说明">{{ baseInfo?.valueExplanation || '无' }}</el-descriptions-item>
            <el-descriptions-item label="长度">{{ baseInfo?.length || '无' }}</el-descriptions-item>
            <el-descriptions-item label="最大值">{{ baseInfo?.maximumValue || '无' }}</el-descriptions-item>
            <el-descriptions-item label="最小值">{{ baseInfo?.minimumValue || '无' }}</el-descriptions-item>
            <el-descriptions-item label="变量备注">{{ baseInfo?.notes || '无' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div v-if="instance" class="mt-5 bg-baf p-4">
          <el-descriptions direction="horizontal" title="统计信息">
            <el-descriptions-item label="参与者或被试的数量">
              {{ instance.participantCount || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="记录数">
              {{ instance.recordCount || '无' }}
            </el-descriptions-item>
            <template v-if="['T', 'S'].includes(baseInfo?.valueType || '')">
              <el-descriptions-item label="平均文本长度">
                {{ instance.averageLength || '无' }}
              </el-descriptions-item>
              <el-descriptions-item label="最长文本长度">
                {{ instance.maxLength || '无' }}
              </el-descriptions-item>
              <el-descriptions-item label="最短文本长度">
                {{ instance.minLength || '无' }}
              </el-descriptions-item>
            </template>

            <template v-if="['N', 'F', 'D', 'DT', 'T'].includes(baseInfo?.valueType || '')">
              <el-descriptions-item label="不重复数值数量">
                {{ instance.distinctValueCount || '无' }}
              </el-descriptions-item>
              <el-descriptions-item label="最小值">
                {{ formatDateTextByType(instance.min, baseInfo?.valueType) }}
              </el-descriptions-item>
              <el-descriptions-item label="最大值">
                {{ formatDateTextByType(instance.max, baseInfo?.valueType) }}
              </el-descriptions-item>
              <el-descriptions-item label="均值">
                {{ instance.stdDev || '无' }}
              </el-descriptions-item>
              <el-descriptions-item label="方差">
                {{ formatDateTextByType(instance.mean, baseInfo?.valueType) }}
              </el-descriptions-item>
            </template>

            <template v-if="['N', 'F'].includes(baseInfo?.valueType || '')">
              <el-descriptions-item label="25%分位数">
                {{ instance.decile2 || '无' }}
              </el-descriptions-item>

              <el-descriptions-item label="中位数">
                {{ instance.median || '无' }}
              </el-descriptions-item>

              <el-descriptions-item label="75%分位数">
                {{ instance.decile7 || '无' }}
              </el-descriptions-item>
            </template>
          </el-descriptions>
        </div>

        <div v-for="(item, index) in navList" :key="index" ref="itemRefs">
          <template v-if="item === '数据' && showData">
            <h3 class="mt-10 text-xl">数据</h3>
            <div class="text-regular mt-5 px-10 py-6">
              <div class="h-[300px]">
                <Chart v-if="baseInfo?.chineseMeaning === '性别'" :option="pieOption" />
                <Chart v-else :option="option" />
              </div>
            </div>
          </template>

          <template v-if="item === '所属类别' && tableData.length > 0">
            <h3 ref="list2" class="mt-[26px] text-xl">所属类别（{{ tableData.length }}）</h3>
            <el-table :data="tableData" style="width: 100%" class="c-table-header mt-4">
              <el-table-column prop="id" label="类别ID" />
              <el-table-column prop="title" label="名称" min-width="100px" />
              <el-table-column prop="dataItemCount" label="数据字段" />
              <el-table-column prop="childrenCount" label="子类别" />
              <el-table-column prop="description" label="说明" />
            </el-table>
          </template>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
  /* 数据字段 */
  import { findAlternativeByMedicalFieldId, getMedicalField, findStatisticByMedicalFieldId } from '@/api/index';
  import Chart from '@/components/Chart.vue';
  import { fieldTypeText, formatDateTextByType } from '@/utils/format';
  import dayjs from 'dayjs';
  import { useRouter } from 'vue-router';
  import { Back } from '@element-plus/icons-vue';
  const router = useRouter();

  interface Props {
    fieldId: string;
    backComponent?: boolean;
  }
  const props = defineProps<Props>();
  const emit = defineEmits<{ backTable: [] }>();
  const loading = ref(false);
  const navList = ref(['数据', '备注', '所属类别']);
  const itemRefs = ref([]);
  const option = reactive<any>({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params: any) {
        // params 是一个数组，每个元素代表一个系列的数据
        let result = '<div style="font-size: 14px;">';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];
          result += `<p>${param.name}：${param.value}</p>`;
          result += `<p>${param.data.percentileFlag}</p>`;
        }
        result += '</div>';
        return result;
      },
    },
    grid: {
      left: 10,
      right: 10,
      bottom: 0,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
      boundaryGap: true,
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#565B5C',
      },
      axisLine: {
        lineStyle: {
          color: '#C8C9CC',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#939899',
      },
    },
    color: ['#1296B3'],
    series: {
      type: 'bar',
      barMaxWidth: '50px',
      data: [],
      label: {
        show: true,
        position: 'top',
      },
    },
  });
  const pieOption = reactive<any>({
    tooltip: {
      trigger: 'item',
    },
    series: {
      type: 'pie',
      radius: '70%',
      data: [] as { value: number; name: string }[],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
      label: {
        color: '#939899',
        formatter: '{b} \n\r{@[]}({d}%)',
      },
    },
  });
  const instance = ref<any>(null);
  const baseInfo = ref<MedicalFieldVO | null>(null);

  async function fetchData() {
    try {
      const { data } = await findStatisticByMedicalFieldId(+props.fieldId!);
      instance.value = data || null;
    } catch (error) {
      console.log(error);
    }
  }

  async function fetchBase() {
    try {
      const { data } = await getMedicalField(+props.fieldId!);
      baseInfo.value = data || null;
    } catch (error) {
      console.log(error);
    }
  }

  const showData = ref(false); //数据
  async function fetchAlternative() {
    try {
      const { data } = await findAlternativeByMedicalFieldId(+props.fieldId!);
      let xData: string[] = [];
      let sData: any[] = [];
      let pieData: any[] = [];

      // 如果是数值型，则按照数值大小排序
      let sortedData = [...data!];
      if (['N', 'F'].includes(baseInfo.value?.valueType || '')) {
        sortedData.sort((a, b) => {
          return Number(a.binMidpoint) - Number(b.binMidpoint);
        });
      } else if (['D', 'DT'].includes(baseInfo.value?.valueType || '')) {
        sortedData.sort((a, b) => {
          return dayjs(a.binMidpoint).valueOf() - dayjs(b.binMidpoint).valueOf();
        });
      } else if (baseInfo.value?.valueType === 'T') {
        sortedData.sort((a, b) => {
          // 如果是文本类型但可以转为时间戳，则按时间排序
          const aTime = dayjs(a.binMidpoint).isValid() ? dayjs(a.binMidpoint).valueOf() : 0;
          const bTime = dayjs(b.binMidpoint).isValid() ? dayjs(b.binMidpoint).valueOf() : 0;
          if (aTime && bTime) {
            return aTime - bTime;
          } else {
            // 否则按字符串排序
            return String(a.binMidpoint).localeCompare(String(b.binMidpoint));
          }
        });
      }

      sortedData.forEach((item) => {
        xData.push(item!.binMidpoint!);
        sData.push({
          value: item.frequency || 0,
          percentileFlag: item.percentileFlag,
        });
        pieData.push({
          name: item.binMidpoint,
          value: item.frequency || 0,
        });
      });
      option.xAxis.data = xData;
      option.series.data = sData;
      pieOption.series.data = pieData;
      showData.value = !!data?.length;
    } catch (error) {
      console.log(error);
    }
  }

  //父类别
  const tableData = ref<CatalogueVO[]>([]);

  const onBack = () => {
    if (props.backComponent) {
      emit('backTable');
    } else {
      router.back();
    }
  };

  watchEffect(async () => {
    if (props.fieldId) {
      try {
        loading.value = true;
        await Promise.all([fetchData(), fetchBase()]);
        //字符S1型数据不用直方图显示
        if (baseInfo.value?.valueType !== 'S1') {
          fetchAlternative();
        }
      } finally {
        loading.value = false;
      }
    }
  });
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: #939899;
    }
  }
</style>
