<template>
  <div class="h-full flex overflow-hidden from-slate-50 to-blue-50 bg-gradient-to-br">
    <!-- 左侧工具面板 -->
    <div class="h-full w-320px flex flex-col border-r border-gray-200/50 bg-white/90 shadow-xl backdrop-blur-sm">
      <!-- 工具搜索 -->
      <div class="border-b border-gray-200/50 from-blue-50 to-indigo-50 bg-gradient-to-r p-6">
        <div class="mb-3">
          <h3 class="text-lg text-gray-800 font-bold">节点工具箱</h3>
          <p class="text-sm text-gray-600">拖拽节点到画布创建工作流</p>
        </div>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索工具..."
          prefix-icon="Search"
          clearable
          class="search-input"
        />
      </div>

      <!-- 工具分类 -->
      <div class="h-0 flex-1">
        <el-scrollbar height="100%" class="p-4">
          <el-collapse v-model="activeCategories" accordion class="custom-collapse">
            <el-collapse-item
              v-for="category in filteredCategories"
              :key="category.name"
              :title="category.name"
              :name="category.name"
              class="category-item"
            >
              <div class="pt-2 space-y-3">
                <div
                  v-for="template in category.templates"
                  :key="template.id"
                  class="node-template group cursor-pointer border border-gray-200/50 rounded-lg p-3 transition-all duration-200"
                  draggable="true"
                  @dragstart="onDragStart($event, template)"
                >
                  <div class="flex items-center gap-3">
                    <div class="node-template-icon h-8 w-8 flex items-center justify-center rounded-lg">
                      <WorkflowIcon :name="template.icon" size="1rem" class="text-white" />
                    </div>
                    <div class="min-w-0 flex-1">
                      <div class="text-sm text-gray-800 font-medium transition-colors group-hover:text-blue-600">
                        {{ template.name }}
                      </div>
                      <div class="line-clamp-2 text-xs text-gray-500 leading-relaxed">{{ template.description }}</div>
                    </div>
                    <div class="opacity-0 transition-opacity group-hover:opacity-100">
                      <div class="i-mdi:drag text-gray-400"></div>
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-scrollbar>
      </div>
    </div>

    <!-- 中间画布区域 -->
    <div class="relative h-full flex-1 from-gray-50 to-slate-100 bg-gradient-to-br">
      <VueFlow
        v-model:nodes="nodes"
        v-model:edges="edges"
        :default-viewport="{ zoom: 1 }"
        :min-zoom="0.2"
        :max-zoom="4"
        @drop="onDrop"
        @dragover="onDragOver"
        @node-click="onNodeClick"
        @edge-click="onEdgeClick"
        @connect="onConnect"
        @nodes-change="onNodesChange"
        @edges-change="onEdgesChange"
        class="workflow-canvas"
      >
        <!-- 背景 -->
        <Background pattern-color="#e2e8f0" :gap="24" />

        <!-- 控制器 -->
        <Controls class="custom-controls" />

        <!-- 自定义节点 -->
        <template #node-input="{ data }">
          <InputNode :data="data" />
        </template>

        <template #node-output="{ data }">
          <OutputNode :data="data" />
        </template>

        <template #node-process="{ data }">
          <ProcessNode :data="data" />
        </template>

        <template #node-condition="{ data }">
          <ConditionNode :data="data" />
        </template>
      </VueFlow>

      <!-- 工具栏 -->
      <div class="absolute left-2 top-2">
        <div class="border border-gray-200/50 rounded-xl bg-white/90 p-4 shadow-lg backdrop-blur-sm">
          <div class="mb-3 text-sm text-gray-700 font-semibold">工作流操作</div>
          <div class="flex gap-2">
            <el-button @click="saveWorkflow" type="primary">
              <el-icon><DocumentAdd /></el-icon>
              保存
            </el-button>
            <el-button @click="exportWorkflow">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button @click="clearWorkflow" type="danger" plain>
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
        </div>
      </div>

      <!-- 缩放控制 -->
      <div class="absolute bottom-4 right-4">
        <div class="flex gap-1 border border-gray-200/50 rounded-xl bg-white/90 p-2 shadow-lg backdrop-blur-sm">
          <el-button @click="zoomIn" size="small" :icon="ZoomIn" class="rounded-md" />
          <el-button @click="zoomOut" size="small" :icon="ZoomOut" class="rounded-md" />
          <el-button @click="fitView" size="small" :icon="FullScreen" class="rounded-md" />
        </div>
      </div>
    </div>

    <!-- 右侧属性面板 -->
    <div class="h-full w-380px border-l border-gray-200/50 bg-white/90 shadow-xl backdrop-blur-sm">
      <el-scrollbar height="100%">
        <div v-if="selectedNode" class="h-full">
          <NodeConfigPanel
            :node="selectedNode"
            @update="onNodeUpdate"
            @delete="onNodeDelete"
            @duplicate="onNodeDuplicate"
          />
        </div>
        <div v-else class="h-full flex items-center justify-center">
          <div class="p-8 text-center">
            <div
              class="mx-auto mb-4 h-16 w-16 flex items-center justify-center rounded-full from-blue-100 to-indigo-100 bg-gradient-to-br"
            >
              <div class="i-mdi:cursor-default-click text-2xl text-blue-500"></div>
            </div>
            <h3 class="mb-2 text-lg text-gray-800 font-semibold">选择节点</h3>
            <p class="text-sm text-gray-500 leading-relaxed">点击画布中的节点来配置其属性和参数</p>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>

  <!-- 导出对话框 -->
  <ExportDialog v-model="exportDialogVisible" :workflow="currentWorkflow" @export="onExport" />
</template>

<script setup lang="ts">
  import { ref, computed, nextTick } from 'vue';
  import { VueFlow, useVueFlow } from '@vue-flow/core';
  import { Background } from '@vue-flow/background';
  import { Controls } from '@vue-flow/controls';

  // 导入样式
  import '@vue-flow/core/dist/style.css';
  import { DocumentAdd, Download, Delete, ZoomIn, ZoomOut, FullScreen } from '@element-plus/icons-vue';
  import type { Node, Edge, Connection } from '@vue-flow/core';
  import type { WorkflowDefinition, NodeTemplate, NodeData } from '@/types/workflow';

  // 导入自定义节点组件
  import InputNode from './nodes/InputNode.vue';
  import OutputNode from './nodes/OutputNode.vue';
  import ProcessNode from './nodes/ProcessNode.vue';
  import ConditionNode from './nodes/ConditionNode.vue';
  import NodeConfigPanel from './NodeConfigPanel.vue';
  import ExportDialog from './ExportDialog.vue';
  import WorkflowIcon from '@/components/common/WorkflowIcon.vue';

  // 导入节点模板
  import { nodeTemplates } from './templates/nodeTemplates';

  const props = defineProps<{
    modelValue?: WorkflowDefinition;
  }>();

  const emit = defineEmits<{
    'update:modelValue': [value: WorkflowDefinition];
    save: [workflow: WorkflowDefinition];
  }>();

  // Vue Flow 实例
  const {
    addNodes,
    addEdges,
    removeNodes,
    removeEdges,
    updateNode,
    zoomIn: flowZoomIn,
    zoomOut: flowZoomOut,
    fitView: flowFitView,
    getNodes,
    getEdges,
  } = useVueFlow();

  // 响应式数据
  const nodes = ref<Node[]>([]);
  const edges = ref<Edge[]>([]);
  const searchKeyword = ref('');
  const activeCategories = ref<string[]>(['输入输出']);
  const selectedNode = ref<NodeData | null>(null);
  const exportDialogVisible = ref(false);

  // 计算属性
  const filteredCategories = computed(() => {
    const categories = new Map<string, { name: string; templates: NodeTemplate[] }>();

    nodeTemplates.forEach((template) => {
      if (
        !searchKeyword.value ||
        template.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        template.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
      ) {
        if (!categories.has(template.category)) {
          categories.set(template.category, {
            name: template.category,
            templates: [],
          });
        }
        categories.get(template.category)!.templates.push(template);
      }
    });

    return Array.from(categories.values());
  });

  const currentWorkflow = computed<WorkflowDefinition>(() => ({
    id: props.modelValue?.id || 'new-workflow',
    name: props.modelValue?.name || '新建工作流',
    description: props.modelValue?.description || '',
    version: props.modelValue?.version || '1.0.0',
    nodes: nodes.value.map((node) => node.data as NodeData),
    edges: edges.value.map((edge) => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      sourceHandle: edge.sourceHandle || '',
      targetHandle: edge.targetHandle || '',
      label: typeof edge.label === 'string' ? edge.label : undefined, // 修复点：强制转换 label 为 string 或 undefined
      animated: edge.animated,
      style: edge.style,
    })),
    metadata: props.modelValue?.metadata || {
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      author: 'current-user',
      tags: [],
    },
    config: props.modelValue?.config || {},
  }));

  // 拖拽处理
  const onDragStart = (event: DragEvent, template: NodeTemplate) => {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/vueflow', JSON.stringify(template));
      event.dataTransfer.effectAllowed = 'move';
    }
  };

  const onDragOver = (event: DragEvent) => {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  };

  const onDrop = (event: DragEvent) => {
    event.preventDefault();

    const templateData = event.dataTransfer?.getData('application/vueflow');
    if (!templateData) return;

    const template: NodeTemplate = JSON.parse(templateData);
    const { x, y } = event;

    // 创建新节点
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: template.type,
      position: { x: x - 150, y: y - 50 }, // 调整位置
      data: {
        id: `node-${Date.now()}`,
        type: template.type,
        label: template.name,
        icon: template.icon,
        config: { ...template.defaultConfig },
        inputs: [...template.inputs],
        outputs: [...template.outputs],
        position: { x: x - 150, y: y - 50 },
        metadata: {
          description: template.description,
          created: new Date().toISOString(),
          updated: new Date().toISOString(),
        },
      } as NodeData,
    };

    addNodes([newNode]);
  };

  // 事件处理
  const onNodeClick = (event: any) => {
    selectedNode.value = event.node.data as NodeData;
  };

  const onEdgeClick = (event: any) => {
    selectedNode.value = null;
  };

  const onConnect = (connection: Connection) => {
    addEdges([
      {
        id: `edge-${Date.now()}`,
        ...connection,
        animated: true,
      },
    ]);
  };

  const onNodesChange = (changes: any[]) => {
    // 处理节点变化
  };

  const onEdgesChange = (changes: any[]) => {
    // 处理连接变化
  };

  // 节点操作
  const onNodeUpdate = (updatedNode: NodeData) => {
    updateNode(updatedNode.id, { data: updatedNode });
    selectedNode.value = updatedNode;
    emit('update:modelValue', currentWorkflow.value);
  };

  const onNodeDelete = (nodeId: string) => {
    removeNodes([nodeId]);
    selectedNode.value = null;
    emit('update:modelValue', currentWorkflow.value);
  };

  const onNodeDuplicate = (node: NodeData) => {
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: node.type,
      position: { x: node.position.x + 50, y: node.position.y + 50 },
      data: {
        ...node,
        id: `node-${Date.now()}`,
        label: `${node.label} (副本)`,
        position: { x: node.position.x + 50, y: node.position.y + 50 },
      } as NodeData,
    };

    addNodes([newNode]);
    emit('update:modelValue', currentWorkflow.value);
  };

  // 工具栏操作
  const saveWorkflow = () => {
    emit('save', currentWorkflow.value);
    emit('update:modelValue', currentWorkflow.value);
  };

  const exportWorkflow = () => {
    exportDialogVisible.value = true;
  };

  const clearWorkflow = () => {
    nodes.value = [];
    edges.value = [];
    selectedNode.value = null;
    emit('update:modelValue', currentWorkflow.value);
  };

  const zoomIn = () => flowZoomIn();
  const zoomOut = () => flowZoomOut();
  const fitView = () => flowFitView();

  const onExport = (format: string, content: string) => {
    // 处理导出
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentWorkflow.value.name}.${format}`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // 初始化
  if (props.modelValue) {
    nextTick(() => {
      // 加载现有工作流
      nodes.value = props.modelValue!.nodes.map((nodeData) => ({
        id: nodeData.id,
        type: nodeData.type,
        position: nodeData.position,
        data: nodeData,
      }));

      edges.value = props.modelValue!.edges.map((edgeData) => ({
        id: edgeData.id,
        source: edgeData.source,
        target: edgeData.target,
        sourceHandle: edgeData.sourceHandle,
        targetHandle: edgeData.targetHandle,
        label: edgeData.label,
        animated: edgeData.animated,
        style: edgeData.style,
      }));
    });
  }
</script>

<style scoped>
  /* 搜索输入框样式 */
  :deep(.search-input .el-input__wrapper) {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
  }

  :deep(.search-input .el-input__wrapper:hover) {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  }

  /* 折叠面板样式 */
  :deep(.custom-collapse) {
    border: none;
    background: transparent;
  }

  :deep(.custom-collapse .el-collapse-item__header) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px 16px;
    font-weight: 600;
    color: #374151;
    transition: all 0.2s ease;
  }

  :deep(.custom-collapse .el-collapse-item__header:hover) {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  :deep(.custom-collapse .el-collapse-item__content) {
    padding: 0;
    border: none;
  }

  /* 节点模板样式 */
  .node-template {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  }

  .node-template:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    transform: translateY(-2px);
  }

  .node-template-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }

  /* Vue Flow 自定义样式 */
  .workflow-canvas {
    background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  }

  :deep(.vue-flow__minimap) {
    border-radius: 0.5rem;
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.5);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
  }

  :deep(.vue-flow__controls) {
    border-radius: 0.5rem;
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.5);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
  }

  :deep(.vue-flow__controls button) {
    border-radius: 0.5rem;
    border: none;
    background: transparent;
    transition: all 0.2s ease;
  }

  :deep(.vue-flow__controls button:hover) {
    background: rgba(59, 130, 246, 0.1);
    transform: scale(1.05);
  }

  /* 连接线样式 */
  :deep(.vue-flow__edge-path) {
    stroke-width: 3;
    stroke: #3b82f6;
    filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.2));
    transition: all 0.2s ease;
  }

  :deep(.vue-flow__edge.animated .vue-flow__edge-path) {
    stroke-dasharray: 8;
    animation: dashdraw 1s linear infinite;
  }

  :deep(.vue-flow__edge.selected .vue-flow__edge-path) {
    stroke: #1d4ed8;
    stroke-width: 4;
    filter: drop-shadow(0 3px 6px rgba(29, 78, 216, 0.3));
  }

  :deep(.vue-flow__edge:hover .vue-flow__edge-path) {
    stroke-width: 4;
    filter: drop-shadow(0 3px 6px rgba(59, 130, 246, 0.3));
  }

  /* Handle连接点样式 */
  :deep(.vue-flow__handle) {
    transition: all 0.2s ease;
  }

  :deep(.vue-flow__handle:hover) {
    transform: scale(1.2);
    box-shadow:
      0 6px 16px rgba(59, 130, 246, 0.5),
      0 0 0 3px rgba(59, 130, 246, 0.3) !important;
  }

  :deep(.vue-flow__handle.connecting) {
    transform: scale(1.3);
    box-shadow:
      0 8px 20px rgba(59, 130, 246, 0.6),
      0 0 0 4px rgba(59, 130, 246, 0.4) !important;
  }

  @keyframes dashdraw {
    to {
      stroke-dashoffset: -16;
    }
  }

  :deep(.el-scrollbar__view) {
    height: 100%;
  }

  /* 自定义滚动条样式 */
  .h-full.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
  }

  .h-full.overflow-y-auto::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  .h-full.overflow-y-auto::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  .h-full.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.5);
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .workflow-editor {
      flex-direction: column;
    }

    .w-320px {
      width: 100%;
      max-height: 200px;
    }

    .w-380px {
      width: 100%;
      max-height: 300px;
    }
  }

  :deep(.vue-flow__node-output) {
    border: none;
  }
</style>
