<template>
  <div :class="iconClass" :style="{ fontSize: size }"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  name: string;
  size?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: '1rem',
});

// 图标映射表 - 确保所有图标都能被 UnoCSS 检测到
const iconMap: Record<string, string> = {
  'text-box': 'i-mdi:text-box',
  'file-upload': 'i-mdi:file-upload',
  'file-download': 'i-mdi:file-download',
  'image': 'i-mdi:image',
  'video': 'i-mdi:video',
  'music': 'i-mdi:music',
  'format-text': 'i-mdi:format-text',
  'translate': 'i-mdi:translate',
  'content-copy': 'i-mdi:content-copy',
  'content-cut': 'i-mdi:content-cut',
  'content-paste': 'i-mdi:content-paste',
  'find-replace': 'i-mdi:find-replace',
  'regex': 'i-mdi:regex',
  'code-json': 'i-mdi:code-json',
  'file-delimited': 'i-mdi:file-delimited',
  'code-braces': 'i-mdi:code-braces',
  'source-branch': 'i-mdi:source-branch',
  'repeat': 'i-mdi:repeat',
  'vector-arrange-below': 'i-mdi:vector-arrange-below',
  'timer': 'i-mdi:timer',
  'web': 'i-mdi:web',
  'database': 'i-mdi:database',
  'email': 'i-mdi:email',
  'import': 'i-mdi:import',
  'export': 'i-mdi:export',
  'cog': 'i-mdi:cog',
  'help-circle': 'i-mdi:help-circle',
  'cursor-default-click': 'i-mdi:cursor-default-click',
  'workflow': 'i-mdi:workflow',
};

const iconClass = computed(() => {
  // 如果传入的是完整的图标类名，直接使用
  if (props.name.startsWith('i-')) {
    return props.name;
  }
  
  // 否则从映射表中查找
  return iconMap[props.name] || 'i-mdi:help-circle';
});
</script>
