import { defineConfig, presetIcons, presetWind3 } from 'unocss';

export default defineConfig({
  presets: [
    presetWind3(),
    presetIcons({
      scale: 1.2,
      warn: true,
      collections: {
        mdi: () => import('@iconify-json/mdi/icons.json').then((i) => i.default),
        uiw: () => import('@iconify-json/uiw/icons.json').then((i) => i.default),
      },
    }),
  ],
  safelist: [
    // 确保所有 nodeTemplates 中使用的图标都被包含
    'i-mdi:text-box-outline',
    'i-mdi:text-box',
    'i-mdi:file-upload',
    'i-mdi:file-download',
    'i-mdi:image',
    'i-mdi:video',
    'i-mdi:music',
    'i-mdi:format-text',
    'i-mdi:translate',
    'i-mdi:content-copy',
    'i-mdi:content-cut',
    'i-mdi:content-paste',
    'i-mdi:find-replace',
    'i-mdi:regex',
    'i-mdi:code-json',
    'i-mdi:file-delimited',
    'i-mdi:code-braces',
    'i-mdi:source-branch',
    'i-mdi:repeat',
    'i-mdi:vector-arrange-below',
    'i-mdi:timer',
    'i-mdi:web',
    'i-mdi:database',
    'i-mdi:email',
    'i-mdi:import',
    'i-mdi:export',
    'i-mdi:cog',
    'i-mdi:help-circle',
    'i-mdi:cursor-default-click',
    'i-mdi:workflow',
    // 数据处理节点图标
    'i-mdi:text-recognition',
    'i-mdi:filter',
    'i-mdi:merge',
    // AI处理节点图标
    'i-mdi:brain',
    'i-mdi:image-search',
  ],
  theme: {
    colors: {
      p: '#007f99',
      m: '#303333',
      tip: '#909399',
      baf: '#f7f9fc',
      bac: '#f0f2f5',
    },
  },
});