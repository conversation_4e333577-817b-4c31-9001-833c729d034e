<template>
  <div class="workflow-test h-screen">
    <!-- 顶部工具栏 -->
    <div class="h-60px flex items-center border-b border-gray-200 bg-white px-6">
      <h1 class="text-xl font-bold">工作流编辑器测试</h1>
      <div class="flex-1"></div>
      <el-button @click="createSample" type="primary">创建示例工作流</el-button>
    </div>

    <!-- 工作流编辑器 -->
    <div class="flex-1">
      <WorkflowEditor v-if="workflow" v-model="workflow" @save="onSave" @update:modelValue="onUpdate" />
      <div v-else class="h-full flex items-center justify-center">
        <div class="text-center">
          <div class="mb-4 text-gray-500">点击上方按钮创建示例工作流</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { ElMessage } from 'element-plus';
  import WorkflowEditor from '@/components/workflow/WorkflowEditor.vue';
  import { useWorkflow } from '@/composables/useWorkflow';
  import type { WorkflowDefinition } from '@/types/workflow';

  const { createWorkflow, saveWorkflow } = useWorkflow();

  const workflow = ref<WorkflowDefinition | null>(null);

  const createSample = () => {
    const sampleWorkflow = createWorkflow('测试工作流', '这是一个测试工作流');

    // 添加一些示例节点
    sampleWorkflow.nodes = [
      {
        id: 'input-1',
        type: 'input',
        label: '文本输入',
        config: {
          placeholder: '请输入文本',
          multiline: false,
          maxLength: 1000,
        },
        inputs: [],
        outputs: [
          {
            id: 'text',
            name: '文本',
            type: 'string',
            required: true,
            description: '输入的文本内容',
          },
        ],
        position: { x: 100, y: 100 },
        metadata: {
          description: '接收用户输入的文本数据',
          created: new Date().toISOString(),
          updated: new Date().toISOString(),
        },
      },
      {
        id: 'process-1',
        type: 'process',
        label: '文本处理',
        config: {
          operation: 'uppercase',
        },
        inputs: [
          {
            id: 'text',
            name: '输入文本',
            type: 'string',
            required: true,
            description: '要处理的文本',
          },
        ],
        outputs: [
          {
            id: 'result',
            name: '处理结果',
            type: 'string',
            required: true,
            description: '处理后的文本',
          },
        ],
        position: { x: 400, y: 100 },
        metadata: {
          description: '处理文本数据',
          created: new Date().toISOString(),
          updated: new Date().toISOString(),
        },
      },
      {
        id: 'output-1',
        type: 'output',
        label: '文本输出',
        config: {
          format: 'plain',
        },
        inputs: [
          {
            id: 'text',
            name: '文本',
            type: 'string',
            required: true,
            description: '要输出的文本',
          },
        ],
        outputs: [],
        position: { x: 700, y: 100 },
        metadata: {
          description: '输出处理后的文本',
          created: new Date().toISOString(),
          updated: new Date().toISOString(),
        },
      },
    ];

    // 添加连接线
    sampleWorkflow.edges = [
      {
        id: 'edge-1',
        source: 'input-1',
        target: 'process-1',
        sourceHandle: 'text',
        targetHandle: 'text',
        animated: true,
      },
      {
        id: 'edge-2',
        source: 'process-1',
        target: 'output-1',
        sourceHandle: 'result',
        targetHandle: 'text',
        animated: true,
      },
    ];

    workflow.value = sampleWorkflow;
    ElMessage.success('示例工作流创建成功');
  };

  const onSave = (workflowData: WorkflowDefinition) => {
    saveWorkflow(workflowData);
    ElMessage.success('工作流保存成功');
  };

  const onUpdate = (workflowData: WorkflowDefinition) => {
    workflow.value = workflowData;
  };
</script>

<style scoped>
  .workflow-test {
    display: flex;
    flex-direction: column;
  }
</style>
