import type { RouteRecordRaw } from 'vue-router';

// 静态路由
export const staticRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/views/index.vue'),
    redirect: '/project',
    children: [
      {
        path: '/project',
        name: 'Project',
        component: () => import('@/views/project/index.vue'),
      },
      {
        path: '/project/detail',
        name: 'ProjectDetail',
        component: () => import('@/views/project/project-detail/index.vue'),
        redirect: '/project/detail/manage',
        props: true,
        children: [
          {
            path: 'settings',
            name: 'ProjectSettings',
            component: () => import('@/views/project/project-detail/settings.vue'),
            props: true,
          },

          {
            path: 'manage',
            name: 'ProjectManage',
            component: () => import('@/views/project/project-detail/manage.vue'),
            props: true,
          },
          {
            path: 'workflow',
            name: 'ProjectWorkflow',
            component: () => import('@/views/project/project-detail/workflow.vue'),
            props: true,
          },
        ],
      },
      {
        path: '/jobs',
        name: 'Jobs',
        component: () => import('@/views/project/project-detail/jobs.vue'),
      },
      {
        path: '/workflow-test',
        name: 'WorkflowTest',
        component: () => import('@/views/workflow-test.vue'),
      },
      {
        path: 'jobs-detail',
        name: 'ProjectJobsDetail',
        component: () => import('@/views/project/project-detail/jobs-detail.vue'),
        props: true,
      },
      {
        path: '/visualization',
        name: 'Visualization',
        component: () => import('@/views/project/project-detail/visualization.vue'),
      },
      {
        path: '/data',
        name: 'Data',
        component: () => import('@/views/data/index.vue'),
      },
      {
        path: '/dataset-field',
        name: 'DatasetField',
        component: () => import('@/views/data/field-list.vue'),
        props: (route) => ({ id: route.query.id }),
      },
      {
        path: '/dataset-field-detail',
        props: (route) => ({ fieldId: route.query.fieldId }),
        name: 'DataSetFieldDetail',
        component: () => import('@/views/data/field-detail.vue'),
      },
      {
        path: '/tools',
        name: 'Tools',
        component: () => import('@/views/tools/index.vue'),
      },
      {
        path: '/tools/tools-detail',
        name: 'ToolsDetail',
        component: () => import('@/views/tools/tools-detail.vue'),
        props: (route) => ({ id: route.query.id }),
      },
      {
        path: '/tools/manage',
        name: 'ToolsManage',
        component: () => import('@/views/tools/manage.vue'),
        meta: {
          requiresAuth: true,
          roles: ['ADMIN'], // 只有管理员可以访问
        },
      },
      {
        path: '/org',
        name: 'Org',
        component: () => import('@/views/org/index.vue'),
      },
      {
        path: '/help',
        name: 'Help',
        component: () => import('@/views/help/index.vue'),
      },
    ],
  },

  //登录注册
  {
    path: '/login',
    redirect: '/login/index',
    component: () => import('@/views/user/user.vue'),
    children: [
      {
        path: 'index',
        name: 'Login',
        component: () => import('@/views/user/login.vue'),
      },
    ],
  },
];
