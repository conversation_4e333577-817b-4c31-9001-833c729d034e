// 工作流节点类型
export type WorkflowNodeType = 
  | 'input'      // 输入节点
  | 'output'     // 输出节点
  | 'process'    // 处理节点
  | 'condition'  // 条件节点
  | 'loop'       // 循环节点
  | 'parallel'   // 并行节点
  | 'merge'      // 合并节点
  | 'custom';    // 自定义节点

// 数据类型
export type DataType = 
  | 'string'
  | 'number'
  | 'boolean'
  | 'array'
  | 'object'
  | 'file'
  | 'image'
  | 'any';

// 参数定义
export interface Parameter {
  id: string;
  name: string;
  type: DataType;
  required: boolean;
  defaultValue?: any;
  description?: string;
  options?: Array<{ label: string; value: any }>; // 用于枚举类型
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

// 工具定义
export interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  icon?: string;
  inputs: Parameter[];
  outputs: Parameter[];
  config?: Record<string, any>;
  version?: string;
  author?: string;
}

// 节点数据
export interface NodeData {
  id: string;
  type: WorkflowNodeType;
  label: string;
  tool?: Tool;
  icon?: string;
  config: Record<string, any>;
  inputs: Parameter[];
  outputs: Parameter[];
  position: { x: number; y: number };
  metadata?: {
    description?: string;
    tags?: string[];
    created?: string;
    updated?: string;
  };
}

// 连接数据
export interface EdgeData {
  id: string;
  source: string;
  target: string;
  sourceHandle: string;
  targetHandle: string;
  label?: string;
  animated?: boolean;
  style?: Record<string, any>;
}

// 工作流定义
export interface WorkflowDefinition {
  id: string;
  name: string;
  description?: string;
  version: string;
  nodes: NodeData[];
  edges: EdgeData[];
  metadata: {
    created: string;
    updated: string;
    author: string;
    tags: string[];
    category?: string;
  };
  config: {
    timeout?: number;
    retryCount?: number;
    parallel?: boolean;
    variables?: Record<string, any>;
  };
}

// 工作流执行状态
export type ExecutionStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

// 工作流执行结果
export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: ExecutionStatus;
  startTime: string;
  endTime?: string;
  inputs: Record<string, any>;
  outputs?: Record<string, any>;
  logs: Array<{
    timestamp: string;
    level: 'info' | 'warn' | 'error';
    message: string;
    nodeId?: string;
  }>;
  error?: {
    message: string;
    stack?: string;
    nodeId?: string;
  };
}

// 标准化输出格式 (支持多种工作流引擎)
export interface StandardWorkflowFormat {
  // 通用格式
  format: 'airflow' | 'prefect' | 'argo' | 'github-actions' | 'azure-pipelines' | 'jenkins';
  version: string;
  content: string | object;
}

// Airflow DAG 格式
export interface AirflowDAG {
  dag_id: string;
  description?: string;
  schedule_interval?: string;
  start_date: string;
  catchup?: boolean;
  tags?: string[];
  default_args?: Record<string, any>;
  tasks: Array<{
    task_id: string;
    operator: string;
    depends_on_past?: boolean;
    upstream_task_ids?: string[];
    params?: Record<string, any>;
  }>;
}

// GitHub Actions 格式
export interface GitHubActionsWorkflow {
  name: string;
  on: Record<string, any>;
  jobs: Record<string, {
    'runs-on': string;
    needs?: string[];
    steps: Array<{
      name?: string;
      uses?: string;
      run?: string;
      with?: Record<string, any>;
      env?: Record<string, any>;
    }>;
  }>;
}

// 节点模板
export interface NodeTemplate {
  id: string;
  name: string;
  type: WorkflowNodeType;
  category: string;
  description: string;
  icon: string;
  defaultConfig: Record<string, any>;
  inputs: Parameter[];
  outputs: Parameter[];
  configSchema?: Record<string, any>; // JSON Schema for validation
}
