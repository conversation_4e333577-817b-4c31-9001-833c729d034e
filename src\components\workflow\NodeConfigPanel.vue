<template>
  <div class="node-config-panel h-full flex flex-col from-white to-gray-50 bg-gradient-to-b">
    <!-- 标题栏 -->
    <div class="panel-header">
      <div class="flex items-center gap-3">
        <div class="node-type-icon">
          <div :class="getNodeIcon(node.type)" class="text-white"></div>
        </div>
        <div class="flex-1">
          <div class="node-title">{{ node.label }}</div>
          <div class="node-type">{{ getNodeTypeName(node.type) }}</div>
        </div>
      </div>
      <div class="flex gap-2">
        <el-button @click="$emit('duplicate', node)" type="primary" size="small"   circle plain >
          <el-icon><CopyDocument /></el-icon>
        </el-button>
        <el-button @click="$emit('delete', node.id)" type="danger" size="small" plain circle>
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 配置内容 -->
    <div class="panel-content flex-1">
      <el-scrollbar height="100%">
        <div class="p-6">
          <!-- 基本信息卡片 -->
          <div class="config-card">
            <div class="config-card-header">
              <div class="i-mdi:information-outline text-blue-500"></div>
              <span>基本信息</span>
            </div>
            <div class="config-card-content">
              <el-form :model="localNode" label-position="top" size="default">
                <el-form-item label="节点名称" class="form-item">
                  <el-input
                    v-model="localNode.label"
                    @change="updateNode"
                    placeholder="请输入节点名称"
                    class="custom-input"
                  />
                </el-form-item>

                <el-form-item label="节点描述" class="form-item">
                  <el-input
                    v-model="localNode.metadata!.description"
                    type="textarea"
                    :rows="3"
                    @change="updateNode"
                    placeholder="请输入节点描述..."
                    class="custom-textarea"
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>

          <!-- 节点特定配置卡片 -->
          <div class="config-card">
            <div class="config-card-header">
              <div class="i-mdi:cog text-purple-500"></div>
              <span>节点配置</span>
            </div>
            <div class="config-card-content">
              <el-form :model="localNode" label-position="top" size="default">
                <!-- 输入节点配置 -->
                <template v-if="node.type === 'input'">
                  <el-form-item label="占位符文本" class="form-item">
                    <el-input
                      v-model="localNode.config.placeholder"
                      @change="updateNode"
                      placeholder="请输入占位符文本"
                      class="custom-input"
                    />
                  </el-form-item>
                  <el-form-item label="多行输入" class="form-item">
                    <div class="switch-container">
                      <el-switch
                        v-model="localNode.config.multiline"
                        @change="updateNode"
                        active-text="启用"
                        inactive-text="禁用"
                      />
                    </div>
                  </el-form-item>
                  <el-form-item label="最大长度" class="form-item">
                    <el-input-number
                      v-model="localNode.config.maxLength"
                      @change="updateNode"
                      :min="1"
                      :max="10000"
                      class="custom-number"
                    />
                  </el-form-item>
                </template>

                <!-- 条件节点配置 -->
                <template v-if="node.type === 'condition'">
                  <el-form-item label="比较操作符" class="form-item">
                    <el-select
                      v-model="localNode.config.operator"
                      @change="updateNode"
                      placeholder="选择操作符"
                      class="custom-select"
                    >
                      <el-option label="等于 (=)" value="equals" />
                      <el-option label="不等于 (≠)" value="notEquals" />
                      <el-option label="大于 (>)" value="greaterThan" />
                      <el-option label="小于 (<)" value="lessThan" />
                      <el-option label="包含" value="contains" />
                      <el-option label="开始于" value="startsWith" />
                      <el-option label="结束于" value="endsWith" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="比较值" class="form-item">
                    <el-input
                      v-model="localNode.config.value"
                      @change="updateNode"
                      placeholder="请输入比较值"
                      class="custom-input"
                    />
                  </el-form-item>
                  <el-form-item label="数据类型" class="form-item">
                    <el-select
                      v-model="localNode.config.dataType"
                      @change="updateNode"
                      placeholder="选择数据类型"
                      class="custom-select"
                    >
                      <el-option label="字符串 (String)" value="string" />
                      <el-option label="数字 (Number)" value="number" />
                      <el-option label="布尔值 (Boolean)" value="boolean" />
                    </el-select>
                  </el-form-item>
                </template>

                <!-- 处理节点配置 -->
                <template v-if="node.type === 'process'">
                  <div v-if="getProcessConfig().length > 0">
                    <el-form-item v-for="config in getProcessConfig()" :key="config.key" :label="config.label">
                      <el-input
                        v-if="config.type === 'text'"
                        v-model="localNode.config[config.key]"
                        @change="updateNode"
                      />
                      <el-input-number
                        v-else-if="config.type === 'number'"
                        v-model="localNode.config[config.key]"
                        @change="updateNode"
                      />
                      <el-switch
                        v-else-if="config.type === 'boolean'"
                        v-model="localNode.config[config.key]"
                        @change="updateNode"
                      />
                      <el-select
                        v-else-if="config.type === 'select'"
                        v-model="localNode.config[config.key]"
                        @change="updateNode"
                      >
                        <el-option
                          v-for="option in config.options"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </template>
              </el-form>
            </div>
          </div>

          <!-- 输入参数配置卡片 -->
          <div class="config-card">
            <div class="config-card-header">
              <div class="i-mdi:format-list-bulleted text-green-500"></div>
              <span>输入参数</span>
            </div>
            <div class="config-card-content">
              <div class="parameters-section">
                <div v-for="(input, index) in localNode.inputs" :key="input.id" class="parameter-item">
                  <div class="parameter-header">
                    <span class="parameter-name">{{ input.name }}</span>
                    <span class="parameter-type">{{ input.type }}</span>
                    <el-tag v-if="input.required" type="danger" size="small">必需</el-tag>
                  </div>
                  <div class="parameter-description">{{ input.description }}</div>

                  <!-- 参数配置 -->
                  <div class="parameter-config">
                    <label class="config-label">默认值</label>
                    <el-input
                      v-model="input.defaultValue"
                      placeholder="设置默认值"
                      @change="updateNode"
                      size="small"
                      class="custom-input"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 输出参数配置卡片 -->
          <div class="config-card">
            <div class="config-card-header">
              <div class="i-mdi:export text-orange-500"></div>
              <span>输出参数</span>
            </div>
            <div class="config-card-content">
              <div class="parameters-section">
                <div v-for="(output, index) in localNode.outputs" :key="output.id" class="parameter-item">
                  <div class="parameter-header">
                    <span class="parameter-name">{{ output.name }}</span>
                    <span class="parameter-type">{{ output.type }}</span>
                  </div>
                  <div class="parameter-description">{{ output.description }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 标签配置卡片 -->
          <div class="config-card">
            <div class="config-card-header">
              <div class="i-mdi:tag text-pink-500"></div>
              <span>标签管理</span>
            </div>
            <div class="config-card-content">
              <el-form :model="localNode" label-position="top" size="default">
                <el-form-item label="节点标签" class="form-item">
                  <div class="tag-container">
                    <el-tag
                      v-for="tag in localNode.metadata!.tags"
                      :key="tag"
                      closable
                      @close="removeTag(tag)"
                      class="tag-item mb-2 mr-2"
                    >
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-if="tagInputVisible"
                      ref="tagInputRef"
                      v-model="tagInputValue"
                      size="small"
                      placeholder="输入标签名称"
                      class="tag-input"
                      @keyup.enter="addTag"
                      @blur="addTag"
                    />
                    <el-button v-else size="small" @click="showTagInput" class="add-tag-btn">
                      <el-icon><Plus /></el-icon>
                      添加标签
                    </el-button>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 操作按钮 -->
    <div class="panel-footer">
      <el-button @click="resetConfig" size="small" class="footer-btn">
        <el-icon><Refresh /></el-icon>
        重置配置
      </el-button>
      <el-button @click="duplicateNode" type="primary" size="small" class="footer-btn">
        <el-icon><CopyDocument /></el-icon>
        复制节点
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, watch, nextTick } from 'vue';
  import { Delete, CopyDocument, Plus, Refresh } from '@element-plus/icons-vue';
  import type { NodeData } from '@/types/workflow';

  const props = defineProps<{
    node: NodeData;
  }>();

  const emit = defineEmits<{
    update: [node: NodeData];
    delete: [nodeId: string];
    duplicate: [node: NodeData];
  }>();

  // 本地节点数据
  const localNode = reactive<NodeData>({
    ...props.node,
    metadata: {
      ...props.node.metadata,
      description: props.node.metadata?.description || '',
      tags: props.node.metadata?.tags || [],
      updated: props.node.metadata?.updated || new Date().toISOString(),
    },
  });

  // 标签输入
  const tagInputVisible = ref(false);
  const tagInputValue = ref('');
  const tagInputRef = ref();

  // 监听属性变化
  watch(
    () => props.node,
    (newNode) => {
      Object.assign(localNode, newNode);
    },
    { deep: true }
  );

  // 更新节点
  const updateNode = () => {
    localNode.metadata!.updated = new Date().toISOString();
    emit('update', { ...localNode });
  };

  // 获取节点图标
  const getNodeIcon = (type: string) => {
    const icons = {
      input: 'i-mdi:import',
      output: 'i-mdi:export',
      process: 'i-mdi:cog',
      condition: 'i-mdi:source-branch',
      loop: 'i-mdi:repeat',
      parallel: 'i-mdi:vector-arrange-below',
    };
    return icons[type as keyof typeof icons] || 'i-mdi:help-circle';
  };

  // 获取节点类型名称
  const getNodeTypeName = (type: string) => {
    const names = {
      input: '输入节点',
      output: '输出节点',
      process: '处理节点',
      condition: '条件节点',
      loop: '循环节点',
      parallel: '并行节点',
    };
    return names[type as keyof typeof names] || '未知节点';
  };

  // 获取处理节点的配置项
  const getProcessConfig = () => {
    // 根据节点的具体类型返回不同的配置项
    const configs: Record<string, any[]> = {
      'text-transform': [
        {
          key: 'operation',
          label: '操作类型',
          type: 'select',
          options: [
            { label: '转大写', value: 'uppercase' },
            { label: '转小写', value: 'lowercase' },
            { label: '去除空格', value: 'trim' },
            { label: '替换文本', value: 'replace' },
          ],
        },
        { key: 'customPattern', label: '自定义模式', type: 'text' },
        { key: 'replacement', label: '替换文本', type: 'text' },
      ],
      'http-request': [
        {
          key: 'method',
          label: '请求方法',
          type: 'select',
          options: [
            { label: 'GET', value: 'GET' },
            { label: 'POST', value: 'POST' },
            { label: 'PUT', value: 'PUT' },
            { label: 'DELETE', value: 'DELETE' },
          ],
        },
        { key: 'url', label: 'URL', type: 'text' },
        { key: 'timeout', label: '超时时间(ms)', type: 'number' },
      ],
    };

    return configs[localNode.id] || [];
  };

  // 标签操作
  const showTagInput = () => {
    tagInputVisible.value = true;
    nextTick(() => {
      tagInputRef.value?.focus();
    });
  };

  const addTag = () => {
    if (tagInputValue.value && !localNode.metadata!.tags?.includes(tagInputValue.value)) {
      if (!localNode.metadata!.tags) {
        localNode.metadata!.tags = [];
      }
      localNode.metadata!.tags.push(tagInputValue.value);
      updateNode();
    }
    tagInputVisible.value = false;
    tagInputValue.value = '';
  };

  const removeTag = (tag: string) => {
    const index = localNode.metadata!.tags?.indexOf(tag);
    if (index !== undefined && index > -1) {
      localNode.metadata!.tags?.splice(index, 1);
      updateNode();
    }
  };

  // 重置配置
  const resetConfig = () => {
    // 重置为默认配置
    Object.assign(localNode, props.node);
    updateNode();
  };

  // 复制节点
  const duplicateNode = () => {
    const newNode: NodeData = {
      ...localNode,
      id: `node-${Date.now()}`,
      label: `${localNode.label} (副本)`,
      position: {
        x: localNode.position.x + 50,
        y: localNode.position.y + 50,
      },
    };
    emit('duplicate', newNode);
  };
</script>

<style scoped>
  .node-config-panel {
    font-family:
      'Inter',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
  }

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  .node-type-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .node-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1e293b;
  }

  .node-type {
    font-size: 0.875rem;
    color: #64748b;
  }

  .config-card {
    margin-bottom: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid #f1f5f9;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .config-card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-bottom: 1px solid #f8fafc;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    font-weight: 600;
    color: #374151;
  }

  .config-card-content {
    padding: 1rem;
  }

  .form-item {
    margin-bottom: 1rem;
  }

  .custom-input :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
  }

  .custom-input :deep(.el-input__wrapper:hover) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .custom-input :deep(.el-input__wrapper.is-focus) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .custom-textarea :deep(.el-textarea__inner) {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
  }

  .custom-textarea :deep(.el-textarea__inner:hover) {
    border-color: #3b82f6;
  }

  .custom-textarea :deep(.el-textarea__inner:focus) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .custom-select :deep(.el-select__wrapper) {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
  }

  .custom-select :deep(.el-select__wrapper:hover) {
    border-color: #3b82f6;
  }

  .custom-select :deep(.el-select__wrapper.is-focused) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .custom-number :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
  }

  .custom-number :deep(.el-input__wrapper:hover) {
    border-color: #3b82f6;
  }

  .custom-number :deep(.el-input__wrapper.is-focus) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .switch-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .tag-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
  }

  .tag-item {
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    border: 1px solid #81d4fa;
    color: #0277bd;
  }

  .tag-input {
    width: 120px !important;
  }

  .tag-input :deep(.el-input__wrapper) {
    border-radius: 6px;
    border: 1px solid #e2e8f0;
  }

  .add-tag-btn {
    border-radius: 0.5rem;
    border: 1px dashed #cbd5e1;
    background: transparent;
    color: #64748b;
    transition: all 0.2s ease;
  }

  .add-tag-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
  }

  .panel-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-top: 1px solid #f1f5f9;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }

  .footer-btn {
    border-radius: 0.5rem;
    transition: all 0.2s ease;
  }

  .footer-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .parameters-section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .parameter-item {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #f1f5f9;
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  }

  .parameter-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .parameter-name {
    font-weight: 500;
    color: #1e293b;
  }

  .parameter-type {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border: 1px solid #93c5fd;
  }

  .parameter-description {
    font-size: 0.875rem;
    color: #64748b;
    line-height: 1.625;
  }

  .parameter-config {
    margin-top: 0.75rem;
  }

  .config-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
  }

  :deep(.el-button.is-circle) {
    width: 36px;
    height: 36px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .panel-header {
      padding: 1rem;
    }

    .config-card-content {
      padding: 0.75rem;
    }

    .node-type-icon {
      width: 2.5rem;
      height: 2.5rem;
    }

    .node-title {
      font-size: 1rem;
    }
  }
</style>
