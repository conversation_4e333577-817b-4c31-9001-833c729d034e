<template>
  <div class="h-0 flex flex-1 flex-col">
    <!-- 顶部工具栏 -->
    <div class="h-60px flex items-center border-b border-gray-200 bg-white px-5">
      <div class="flex flex-1 items-center gap-4">
        <a class="flex cursor-pointer items-center gap-2 text-gray-500 hover:text-gray-700" @click="router.back()">
          <el-icon size="20px"><ArrowLeftBold /></el-icon>
        </a>
        <div class="flex items-center gap-2">
          <div class="i-mdi:workflow text-xl text-blue-500"></div>
          <el-input v-model="workflowName" placeholder="工作流名称" class="w-300px" @change="updateWorkflowName" />
        </div>
      </div>

      <div class="flex items-center gap-3">
        <el-button @click="validateCurrentWorkflow" size="small">
          <el-icon><CircleCheck /></el-icon>
          验证
        </el-button>
        <el-button @click="saveCurrentWorkflow" type="primary" size="small">
          <el-icon><DocumentAdd /></el-icon>
          保存
        </el-button>
        <el-button @click="showExecuteDialog" type="success" size="small">
          <el-icon><VideoPlay /></el-icon>
          执行
        </el-button>
      </div>
    </div>

    <!-- 工作流编辑器 -->
    <div class="h-0 flex-1">
      <WorkflowEditor
        v-if="currentWorkflow"
        v-model="currentWorkflow"
        @save="onWorkflowSave"
        @update:modelValue="onWorkflowUpdate"
      />
    </div>
  </div>

  <!-- 执行对话框 -->
  <el-dialog v-model="executeDialogVisible" title="执行工作流" width="600px">
    <div class="space-y-4">
      <div>
        <h4 class="mb-2 text-sm font-medium">输入参数</h4>
        <div v-if="inputParameters.length === 0" class="text-sm text-gray-500">此工作流没有输入参数</div>
        <div v-else class="space-y-3">
          <div v-for="param in inputParameters" :key="param.id" class="flex items-center gap-3">
            <label class="w-100px text-sm">{{ param.name }}</label>
            <el-input v-model="executionInputs[param.id]" :placeholder="param.description" class="flex-1" />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="executeDialogVisible = false">取消</el-button>
        <el-button @click="executeWorkflow" type="primary" :loading="executing">
          {{ executing ? '执行中...' : '开始执行' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 验证结果对话框 -->
  <el-dialog v-model="validationDialogVisible" title="验证结果" width="500px">
    <div class="space-y-3">
      <div class="flex items-center gap-2">
        <el-icon :class="validationResult.valid ? 'text-green-500' : 'text-red-500'" size="20">
          <component :is="validationResult.valid ? 'CircleCheck' : 'CircleClose'" />
        </el-icon>
        <span class="font-medium">
          {{ validationResult.valid ? '工作流验证通过' : '工作流验证失败' }}
        </span>
      </div>

      <div v-if="!validationResult.valid && validationResult.errors.length > 0">
        <h4 class="mb-2 text-sm font-medium">错误列表：</h4>
        <ul class="space-y-1">
          <li v-for="error in validationResult.errors" :key="error" class="text-sm text-red-600">• {{ error }}</li>
        </ul>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="validationDialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import { ArrowLeftBold, CircleCheck, CircleClose, DocumentAdd, VideoPlay } from '@element-plus/icons-vue';

  import WorkflowEditor from '@/components/workflow/WorkflowEditor.vue';
  import { useWorkflow } from '@/composables/useWorkflow';
  import type { WorkflowDefinition, Parameter } from '@/types/workflow';

  const router = useRouter();
  const { createWorkflow, saveWorkflow, validateWorkflow, executeWorkflow: runWorkflow } = useWorkflow();

  // 工作流状态
  const workflowName = ref('新建工作流');
  const currentWorkflow = ref<WorkflowDefinition | null>(null);

  // 对话框状态
  const executeDialogVisible = ref(false);
  const validationDialogVisible = ref(false);
  const executing = ref(false);

  // 执行相关
  const executionInputs = ref<Record<string, any>>({});
  const validationResult = ref<{ valid: boolean; errors: string[] }>({ valid: true, errors: [] });

  // 计算属性
  const inputParameters = computed<Parameter[]>(() => {
    if (!currentWorkflow.value) return [];

    return currentWorkflow.value.nodes.filter((node) => node.type === 'input').flatMap((node) => node.outputs);
  });

  // 初始化工作流
  const initializeWorkflow = () => {
    const workflow = createWorkflow(workflowName.value);
    currentWorkflow.value = workflow;
  };

  // 更新工作流名称
  const updateWorkflowName = () => {
    if (currentWorkflow.value) {
      currentWorkflow.value.name = workflowName.value;
      currentWorkflow.value.metadata.updated = new Date().toISOString();
    }
  };

  // 工作流事件处理
  const onWorkflowSave = (workflow: WorkflowDefinition) => {
    saveWorkflow(workflow);
    ElMessage.success('工作流保存成功');
  };

  const onWorkflowUpdate = (workflow: WorkflowDefinition) => {
    currentWorkflow.value = workflow;
    workflowName.value = workflow.name;
  };

  // 保存当前工作流
  const saveCurrentWorkflow = () => {
    if (!currentWorkflow.value) {
      ElMessage.warning('没有可保存的工作流');
      return;
    }

    saveWorkflow(currentWorkflow.value);
    ElMessage.success('工作流保存成功');
  };

  // 验证工作流
  const validateCurrentWorkflow = () => {
    if (!currentWorkflow.value) {
      ElMessage.warning('没有可验证的工作流');
      return;
    }

    validationResult.value = validateWorkflow(currentWorkflow.value);
    validationDialogVisible.value = true;
  };

  // 显示执行对话框
  const showExecuteDialog = () => {
    if (!currentWorkflow.value) {
      ElMessage.warning('没有可执行的工作流');
      return;
    }

    // 验证工作流
    const validation = validateWorkflow(currentWorkflow.value);
    if (!validation.valid) {
      ElMessage.error('工作流验证失败，请先修复错误');
      validationResult.value = validation;
      validationDialogVisible.value = true;
      return;
    }

    // 初始化输入参数
    executionInputs.value = {};
    inputParameters.value.forEach((param) => {
      executionInputs.value[param.id] = param.defaultValue || '';
    });

    executeDialogVisible.value = true;
  };

  // 执行工作流
  const executeWorkflow = async () => {
    if (!currentWorkflow.value) return;

    executing.value = true;

    try {
      const execution = await runWorkflow(currentWorkflow.value, executionInputs.value);

      if (execution.status === 'completed') {
        ElMessage.success('工作流执行完成');
      } else if (execution.status === 'failed') {
        ElMessage.error(`工作流执行失败: ${execution.error?.message}`);
      }

      executeDialogVisible.value = false;
    } catch (error) {
      ElMessage.error('工作流执行出错');
      console.error('Workflow execution error:', error);
    } finally {
      executing.value = false;
    }
  };

  // 监听工作流名称变化
  watch(workflowName, (newName) => {
    if (currentWorkflow.value) {
      currentWorkflow.value.name = newName;
    }
  });

  // 初始化
  initializeWorkflow();
</script>
