{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["vite/client", "element-plus/global"], "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.d.ts", "./auto-imports.d.ts", "./components.d.ts"]}