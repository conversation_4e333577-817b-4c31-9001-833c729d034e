<template>
  <div v-loading="loading" class="h-full flex flex-col p-4">
    <!-- <div class="flex">
      <el-input
        v-model="fieldName"
        placeholder="请输入关键字搜索"
        style="width: 300px"
        clearable
        @clear="fetchData()"
        @keyup.enter="fetchData()"
      >
        <template #append>
          <el-button :icon="Search" @click="fetchData()" />
        </template>
      </el-input>
    </div> -->

    <el-table :data="tableData" style="width: 100%" class="c-table-header h-0 flex-1">
      <el-table-column prop="datasetNameCn" min-width="100px" label="数据集名称(中文)" show-overflow-tooltip />
      <el-table-column prop="projectCode" label="课题编码缩写" />
      <el-table-column prop="createDate" label="更新日期" width="170px" />
      <el-table-column prop="projectLeader" label="项目负责人" />
      <el-table-column prop="affiliatedUnit" label="所属单位" />
      <el-table-column prop="state" label="状态" />
      <el-table-column fixed="right" label="操作" width="200px">
        <template #default="{ row }">
          <el-button link type="primary" @click="onViewDetail(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-bottom">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :page-size="pagination.pageSize"
        :total="total"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Search } from '@element-plus/icons-vue';
  import { findFileInforByUserId } from '@/api';
  import { useRouter } from 'vue-router';
  import { useUsers } from '@/store/index';
  const router = useRouter();
  const store = useUsers();

  //表格
  const fieldName = ref('');
  const loading = ref(false);
  const tableData = ref<FileInfoVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchData(page);
  };

  const fetchData = async (pageNum?: number) => {
    try {
      loading.value = true;
      // 如果没有指定页码，使用当前页码
      const currentPage = pageNum !== undefined ? pageNum : pagination.page;
      const { data } = await findFileInforByUserId(store.user.id, currentPage, pagination.pageSize);
      total.value = data!.totalElement!;
      tableData.value = data?.content || [];
      // 更新当前页码
      pagination.page = currentPage;
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  //查看详情
  const onViewDetail = (row: FileInfoVO) => {
    router.push({ name: 'DatasetField', query: { id: row.id } });
  };

  onBeforeMount(() => {
    fetchData();
  });
</script>
