import { ref, computed } from 'vue';
import type { WorkflowDefinition, WorkflowExecution, StandardWorkflowFormat } from '@/types/workflow';

// 工作流存储
const workflows = ref<WorkflowDefinition[]>([]);
const currentWorkflow = ref<WorkflowDefinition | null>(null);
const executions = ref<WorkflowExecution[]>([]);

export function useWorkflow() {
  // 创建新工作流
  const createWorkflow = (name: string, description?: string): WorkflowDefinition => {
    const workflow: WorkflowDefinition = {
      id: `workflow-${Date.now()}`,
      name,
      description: description || '',
      version: '1.0.0',
      nodes: [],
      edges: [],
      metadata: {
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        author: 'current-user', // 这里应该从用户状态获取
        tags: []
      },
      config: {
        timeout: 300000, // 5分钟
        retryCount: 3,
        parallel: false,
        variables: {}
      }
    };
    
    workflows.value.push(workflow);
    currentWorkflow.value = workflow;
    return workflow;
  };

  // 保存工作流
  const saveWorkflow = (workflow: WorkflowDefinition) => {
    const index = workflows.value.findIndex(w => w.id === workflow.id);
    if (index >= 0) {
      workflows.value[index] = { ...workflow, metadata: { ...workflow.metadata, updated: new Date().toISOString() } };
    } else {
      workflows.value.push(workflow);
    }
    
    // 保存到本地存储
    localStorage.setItem('workflows', JSON.stringify(workflows.value));
    return workflow;
  };

  // 加载工作流
  const loadWorkflow = (id: string): WorkflowDefinition | null => {
    const workflow = workflows.value.find(w => w.id === id);
    if (workflow) {
      currentWorkflow.value = workflow;
    }
    return workflow || null;
  };

  // 删除工作流
  const deleteWorkflow = (id: string) => {
    const index = workflows.value.findIndex(w => w.id === id);
    if (index >= 0) {
      workflows.value.splice(index, 1);
      if (currentWorkflow.value?.id === id) {
        currentWorkflow.value = null;
      }
      localStorage.setItem('workflows', JSON.stringify(workflows.value));
    }
  };

  // 复制工作流
  const duplicateWorkflow = (id: string): WorkflowDefinition | null => {
    const original = workflows.value.find(w => w.id === id);
    if (!original) return null;

    const duplicate: WorkflowDefinition = {
      ...original,
      id: `workflow-${Date.now()}`,
      name: `${original.name} (副本)`,
      metadata: {
        ...original.metadata,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    };

    workflows.value.push(duplicate);
    localStorage.setItem('workflows', JSON.stringify(workflows.value));
    return duplicate;
  };

  // 验证工作流
  const validateWorkflow = (workflow: WorkflowDefinition): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // 检查基本信息
    if (!workflow.name.trim()) {
      errors.push('工作流名称不能为空');
    }

    // 检查节点
    if (workflow.nodes.length === 0) {
      errors.push('工作流至少需要一个节点');
    }

    // 检查输入输出节点
    const inputNodes = workflow.nodes.filter(n => n.type === 'input');
    const outputNodes = workflow.nodes.filter(n => n.type === 'output');
    
    if (inputNodes.length === 0) {
      errors.push('工作流需要至少一个输入节点');
    }
    
    if (outputNodes.length === 0) {
      errors.push('工作流需要至少一个输出节点');
    }

    // 检查连接
    const nodeIds = new Set(workflow.nodes.map(n => n.id));
    for (const edge of workflow.edges) {
      if (!nodeIds.has(edge.source)) {
        errors.push(`连接源节点 ${edge.source} 不存在`);
      }
      if (!nodeIds.has(edge.target)) {
        errors.push(`连接目标节点 ${edge.target} 不存在`);
      }
    }

    // 检查循环依赖
    if (hasCyclicDependency(workflow)) {
      errors.push('工作流存在循环依赖');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  };

  // 检查循环依赖
  const hasCyclicDependency = (workflow: WorkflowDefinition): boolean => {
    const graph = new Map<string, string[]>();
    
    // 构建邻接表
    workflow.nodes.forEach(node => {
      graph.set(node.id, []);
    });
    
    workflow.edges.forEach(edge => {
      graph.get(edge.source)?.push(edge.target);
    });

    // DFS检查循环
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      visited.add(nodeId);
      recursionStack.add(nodeId);

      const neighbors = graph.get(nodeId) || [];
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          if (hasCycle(neighbor)) return true;
        } else if (recursionStack.has(neighbor)) {
          return true;
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const nodeId of graph.keys()) {
      if (!visited.has(nodeId)) {
        if (hasCycle(nodeId)) return true;
      }
    }

    return false;
  };

  // 执行工作流
  const executeWorkflow = async (workflow: WorkflowDefinition, inputs: Record<string, any>): Promise<WorkflowExecution> => {
    const execution: WorkflowExecution = {
      id: `execution-${Date.now()}`,
      workflowId: workflow.id,
      status: 'running',
      startTime: new Date().toISOString(),
      inputs,
      logs: [
        {
          timestamp: new Date().toISOString(),
          level: 'info',
          message: '开始执行工作流'
        }
      ]
    };

    executions.value.push(execution);

    try {
      // 这里应该实现实际的执行逻辑
      // 目前只是模拟执行
      await simulateExecution(workflow, execution);
      
      execution.status = 'completed';
      execution.endTime = new Date().toISOString();
      execution.logs.push({
        timestamp: new Date().toISOString(),
        level: 'info',
        message: '工作流执行完成'
      });
    } catch (error) {
      execution.status = 'failed';
      execution.endTime = new Date().toISOString();
      execution.error = {
        message: error instanceof Error ? error.message : '未知错误'
      };
      execution.logs.push({
        timestamp: new Date().toISOString(),
        level: 'error',
        message: `工作流执行失败: ${execution.error.message}`
      });
    }

    return execution;
  };

  // 模拟执行
  const simulateExecution = async (workflow: WorkflowDefinition, execution: WorkflowExecution) => {
    // 按拓扑顺序执行节点
    const executionOrder = getExecutionOrder(workflow);
    
    for (const nodeId of executionOrder) {
      const node = workflow.nodes.find(n => n.id === nodeId);
      if (!node) continue;

      execution.logs.push({
        timestamp: new Date().toISOString(),
        level: 'info',
        message: `执行节点: ${node.label}`,
        nodeId: node.id
      });

      // 模拟节点执行时间
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
    }
  };

  // 获取执行顺序
  const getExecutionOrder = (workflow: WorkflowDefinition): string[] => {
    const graph = new Map<string, string[]>();
    const inDegree = new Map<string, number>();

    // 初始化
    workflow.nodes.forEach(node => {
      graph.set(node.id, []);
      inDegree.set(node.id, 0);
    });

    // 构建图
    workflow.edges.forEach(edge => {
      graph.get(edge.source)?.push(edge.target);
      inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1);
    });

    // 拓扑排序
    const queue: string[] = [];
    const result: string[] = [];

    // 找到所有入度为0的节点
    for (const [nodeId, degree] of inDegree) {
      if (degree === 0) {
        queue.push(nodeId);
      }
    }

    while (queue.length > 0) {
      const current = queue.shift()!;
      result.push(current);

      const neighbors = graph.get(current) || [];
      for (const neighbor of neighbors) {
        const newDegree = (inDegree.get(neighbor) || 0) - 1;
        inDegree.set(neighbor, newDegree);
        if (newDegree === 0) {
          queue.push(neighbor);
        }
      }
    }

    return result;
  };

  // 导出为标准格式
  const exportToStandardFormat = (workflow: WorkflowDefinition, format: string): StandardWorkflowFormat => {
    switch (format) {
      case 'airflow':
        return {
          format: 'airflow',
          version: '2.0',
          content: generateAirflowDAG(workflow)
        };
      case 'github-actions':
        return {
          format: 'github-actions',
          version: '1.0',
          content: generateGitHubActions(workflow)
        };
      default:
        return {
          format: 'github-actions',
          version: '1.0',
          content: JSON.stringify(workflow, null, 2)
        };
    }
  };

  // 生成Airflow DAG (简化版)
  const generateAirflowDAG = (workflow: WorkflowDefinition) => {
    return {
      dag_id: workflow.name.toLowerCase().replace(/\s+/g, '_'),
      description: workflow.description,
      schedule_interval: '@daily',
      start_date: workflow.metadata.created,
      tasks: workflow.nodes.map(node => ({
        task_id: node.id,
        operator: 'PythonOperator',
        python_callable: `execute_${node.type}`,
        params: node.config
      }))
    };
  };

  // 生成GitHub Actions (简化版)
  const generateGitHubActions = (workflow: WorkflowDefinition) => {
    return {
      name: workflow.name,
      on: ['push'],
      jobs: workflow.nodes.reduce((jobs, node) => {
        jobs[node.id] = {
          'runs-on': 'ubuntu-latest',
          steps: [
            {
              name: node.label,
              run: `echo "Executing ${node.label}"`
            }
          ]
        };
        return jobs;
      }, {} as Record<string, any>)
    };
  };

  // 从本地存储加载
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('workflows');
      if (stored) {
        workflows.value = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load workflows from storage:', error);
    }
  };

  // 计算属性
  const workflowCount = computed(() => workflows.value.length);
  const recentWorkflows = computed(() => 
    workflows.value
      .sort((a, b) => new Date(b.metadata.updated).getTime() - new Date(a.metadata.updated).getTime())
      .slice(0, 5)
  );

  // 初始化时加载数据
  loadFromStorage();

  return {
    // 状态
    workflows,
    currentWorkflow,
    executions,
    
    // 计算属性
    workflowCount,
    recentWorkflows,
    
    // 方法
    createWorkflow,
    saveWorkflow,
    loadWorkflow,
    deleteWorkflow,
    duplicateWorkflow,
    validateWorkflow,
    executeWorkflow,
    exportToStandardFormat,
    loadFromStorage
  };
}
