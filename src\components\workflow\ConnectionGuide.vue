<template>
  <div class="connection-guide" v-if="showGuide">
    <div class="guide-overlay" @click="hideGuide"></div>
    <div class="guide-content">
      <div class="guide-header">
        <h3>连接节点指南</h3>
        <el-button @click="hideGuide" type="text" class="close-btn">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="guide-body">
        <div class="guide-step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>拖拽连接</h4>
            <p>从源节点的输出端口（右侧圆点）拖拽到目标节点的输入端口（左侧圆点）</p>
          </div>
        </div>

        <div class="guide-step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>数据类型匹配</h4>
            <p>连接时会自动验证数据类型兼容性，不兼容的连接会被阻止</p>
            <div class="type-colors">
              <span class="type-badge string">string</span>
              <span class="type-badge number">number</span>
              <span class="type-badge boolean">boolean</span>
              <span class="type-badge array">array</span>
              <span class="type-badge object">object</span>
              <span class="type-badge file">file</span>
              <span class="type-badge any">any</span>
            </div>
          </div>
        </div>

        <div class="guide-step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>管理连接</h4>
            <p>点击连接线可以选中，使用工具栏的"清除连接"按钮可以删除所有连接</p>
          </div>
        </div>
      </div>

      <div class="guide-footer">
        <el-checkbox v-model="dontShowAgain">不再显示此指南</el-checkbox>
        <el-button @click="hideGuide" type="primary">知道了</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { Close } from '@element-plus/icons-vue';

  const showGuide = ref(false);
  const dontShowAgain = ref(false);

  const emit = defineEmits<{
    close: [];
  }>();

  onMounted(() => {
    // 检查是否已经显示过指南
    const hasShownGuide = localStorage.getItem('workflow-connection-guide-shown');
    if (!hasShownGuide) {
      showGuide.value = true;
    }
  });

  const hideGuide = () => {
    showGuide.value = false;
    if (dontShowAgain.value) {
      localStorage.setItem('workflow-connection-guide-shown', 'true');
    }
    emit('close');
  };

  // 暴露方法供父组件调用
  defineExpose({
    show: () => {
      showGuide.value = true;
    }
  });
</script>

<style scoped>
  .connection-guide {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .guide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }

  .guide-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
  }

  .guide-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
  }

  .guide-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }

  .close-btn {
    padding: 4px;
    color: #6b7280;
  }

  .guide-body {
    padding: 20px 24px;
  }

  .guide-step {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
  }

  .guide-step:last-child {
    margin-bottom: 0;
  }

  .step-number {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
  }

  .step-content h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }

  .step-content p {
    margin: 0 0 12px 0;
    color: #6b7280;
    line-height: 1.5;
  }

  .type-colors {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    font-family: 'JetBrains Mono', 'Consolas', monospace;
  }

  .type-badge.string {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
  }

  .type-badge.number {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
  }

  .type-badge.boolean {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
  }

  .type-badge.array {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
  }

  .type-badge.object {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
  }

  .type-badge.file {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
  }

  .type-badge.any {
    background: rgba(100, 116, 139, 0.2);
    color: #64748b;
  }

  .guide-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px 20px;
    border-top: 1px solid #e5e7eb;
  }
</style>
