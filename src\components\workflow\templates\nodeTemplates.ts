import type { NodeTemplate } from '@/types/workflow';

export const nodeTemplates: NodeTemplate[] = [
  // 输入输出节点
  {
    id: 'input-text',
    name: '文本输入',
    type: 'input',
    category: '输入输出',
    description: '接收文本数据输入',
    icon: 'i-mdi:text-box',
    defaultConfig: {
      placeholder: '请输入文本',
      multiline: false,
      maxLength: 1000,
    },
    inputs: [],
    outputs: [
      {
        id: 'text',
        name: '文本',
        type: 'string',
        required: true,
        description: '输出的文本内容',
      },
    ],
  },
  {
    id: 'input-file',
    name: '文件输入',
    type: 'input',
    category: '输入输出',
    description: '接收文件数据输入',
    icon: 'i-mdi:file-upload',
    defaultConfig: {
      acceptTypes: ['.txt', '.csv', '.json'],
      maxSize: 10485760, // 10MB
      multiple: false,
    },
    inputs: [],
    outputs: [
      {
        id: 'file',
        name: '文件',
        type: 'file',
        required: true,
        description: '上传的文件',
      },
    ],
  },
  {
    id: 'output-text',
    name: '文本输出',
    type: 'output',
    category: '输入输出',
    description: '输出文本数据',
    icon: 'i-mdi:text-box-outline',
    defaultConfig: {
      format: 'plain',
      encoding: 'utf-8',
    },
    inputs: [
      {
        id: 'text',
        name: '文本',
        type: 'string',
        required: true,
        description: '要输出的文本内容',
      },
    ],
    outputs: [],
  },
  {
    id: 'output-file',
    name: '文件输出',
    type: 'output',
    category: '输入输出',
    description: '输出文件数据',
    icon: 'i-mdi:file-download',
    defaultConfig: {
      filename: 'output.txt',
      format: 'auto',
    },
    inputs: [
      {
        id: 'data',
        name: '数据',
        type: 'any',
        required: true,
        description: '要保存的数据',
      },
    ],
    outputs: [],
  },

  // 数据处理节点
  {
    id: 'text-transform',
    name: '文本转换',
    type: 'process',
    category: '数据处理',
    description: '对文本进行各种转换操作',
    icon: 'i-mdi:text-recognition',
    defaultConfig: {
      operation: 'uppercase',
      customPattern: '',
      replacement: '',
    },
    inputs: [
      {
        id: 'text',
        name: '输入文本',
        type: 'string',
        required: true,
        description: '要转换的文本',
      },
    ],
    outputs: [
      {
        id: 'result',
        name: '转换结果',
        type: 'string',
        required: true,
        description: '转换后的文本',
      },
    ],
  },
  {
    id: 'data-filter',
    name: '数据过滤',
    type: 'process',
    category: '数据处理',
    description: '根据条件过滤数据',
    icon: 'i-mdi:filter',
    defaultConfig: {
      filterType: 'contains',
      filterValue: '',
      caseSensitive: false,
    },
    inputs: [
      {
        id: 'data',
        name: '输入数据',
        type: 'array',
        required: true,
        description: '要过滤的数据数组',
      },
    ],
    outputs: [
      {
        id: 'filtered',
        name: '过滤结果',
        type: 'array',
        required: true,
        description: '过滤后的数据',
      },
    ],
  },
  {
    id: 'data-merge',
    name: '数据合并',
    type: 'process',
    category: '数据处理',
    description: '合并多个数据源',
    icon: 'i-mdi:merge',
    defaultConfig: {
      mergeType: 'concat',
      key: 'id',
    },
    inputs: [
      {
        id: 'data1',
        name: '数据源1',
        type: 'array',
        required: true,
        description: '第一个数据源',
      },
      {
        id: 'data2',
        name: '数据源2',
        type: 'array',
        required: true,
        description: '第二个数据源',
      },
    ],
    outputs: [
      {
        id: 'merged',
        name: '合并结果',
        type: 'array',
        required: true,
        description: '合并后的数据',
      },
    ],
  },

  // AI处理节点
  {
    id: 'text-analysis',
    name: '文本分析',
    type: 'process',
    category: 'AI处理',
    description: '使用AI进行文本分析',
    icon: 'i-mdi:brain',
    defaultConfig: {
      analysisType: 'sentiment',
      model: 'default',
      confidence: 0.8,
    },
    inputs: [
      {
        id: 'text',
        name: '文本',
        type: 'string',
        required: true,
        description: '要分析的文本',
      },
    ],
    outputs: [
      {
        id: 'result',
        name: '分析结果',
        type: 'object',
        required: true,
        description: '分析结果对象',
      },
    ],
  },
  {
    id: 'image-recognition',
    name: '图像识别',
    type: 'process',
    category: 'AI处理',
    description: '使用AI进行图像识别',
    icon: 'i-mdi:image-search',
    defaultConfig: {
      model: 'yolo',
      confidence: 0.7,
      maxObjects: 10,
    },
    inputs: [
      {
        id: 'image',
        name: '图像',
        type: 'image',
        required: true,
        description: '要识别的图像',
      },
    ],
    outputs: [
      {
        id: 'objects',
        name: '识别对象',
        type: 'array',
        required: true,
        description: '识别到的对象列表',
      },
    ],
  },

  // 控制流节点
  {
    id: 'condition',
    name: '条件判断',
    type: 'condition',
    category: '控制流',
    description: '根据条件进行分支处理',
    icon: 'i-mdi:source-branch',
    defaultConfig: {
      operator: 'equals',
      value: '',
      dataType: 'string',
    },
    inputs: [
      {
        id: 'input',
        name: '输入值',
        type: 'any',
        required: true,
        description: '要判断的值',
      },
    ],
    outputs: [
      {
        id: 'true',
        name: '真分支',
        type: 'any',
        required: false,
        description: '条件为真时的输出',
      },
      {
        id: 'false',
        name: '假分支',
        type: 'any',
        required: false,
        description: '条件为假时的输出',
      },
    ],
  },
  {
    id: 'loop',
    name: '循环处理',
    type: 'loop',
    category: '控制流',
    description: '对数据进行循环处理',
    icon: 'i-mdi:repeat',
    defaultConfig: {
      loopType: 'forEach',
      maxIterations: 1000,
      breakCondition: '',
    },
    inputs: [
      {
        id: 'items',
        name: '循环项',
        type: 'array',
        required: true,
        description: '要循环处理的数据',
      },
    ],
    outputs: [
      {
        id: 'result',
        name: '处理结果',
        type: 'array',
        required: true,
        description: '循环处理的结果',
      },
    ],
  },
  {
    id: 'parallel',
    name: '并行处理',
    type: 'parallel',
    category: '控制流',
    description: '并行执行多个任务',
    icon: 'i-mdi:vector-arrange-below',
    defaultConfig: {
      maxConcurrency: 4,
      timeout: 30000,
    },
    inputs: [
      {
        id: 'tasks',
        name: '任务列表',
        type: 'array',
        required: true,
        description: '要并行执行的任务',
      },
    ],
    outputs: [
      {
        id: 'results',
        name: '执行结果',
        type: 'array',
        required: true,
        description: '所有任务的执行结果',
      },
    ],
  },

  // 工具节点
  {
    id: 'http-request',
    name: 'HTTP请求',
    type: 'process',
    category: '工具',
    description: '发送HTTP请求',
    icon: 'i-mdi:web',
    defaultConfig: {
      method: 'GET',
      url: '',
      headers: {},
      timeout: 10000,
    },
    inputs: [
      {
        id: 'url',
        name: 'URL',
        type: 'string',
        required: true,
        description: '请求的URL',
      },
      {
        id: 'data',
        name: '请求数据',
        type: 'object',
        required: false,
        description: '请求体数据',
      },
    ],
    outputs: [
      {
        id: 'response',
        name: '响应',
        type: 'object',
        required: true,
        description: 'HTTP响应',
      },
    ],
  },
  {
    id: 'database-query',
    name: '数据库查询',
    type: 'process',
    category: '工具',
    description: '执行数据库查询',
    icon: 'i-mdi:database',
    defaultConfig: {
      connectionString: '',
      query: 'SELECT * FROM table',
      parameters: {},
    },
    inputs: [
      {
        id: 'query',
        name: 'SQL查询',
        type: 'string',
        required: true,
        description: 'SQL查询语句',
      },
    ],
    outputs: [
      {
        id: 'result',
        name: '查询结果',
        type: 'array',
        required: true,
        description: '查询结果集',
      },
    ],
  },
  {
    id: 'email-send',
    name: '发送邮件',
    type: 'process',
    category: '工具',
    description: '发送电子邮件',
    icon: 'i-mdi:email',
    defaultConfig: {
      smtpServer: '',
      port: 587,
      username: '',
      password: '',
    },
    inputs: [
      {
        id: 'to',
        name: '收件人',
        type: 'string',
        required: true,
        description: '收件人邮箱地址',
      },
      {
        id: 'subject',
        name: '主题',
        type: 'string',
        required: true,
        description: '邮件主题',
      },
      {
        id: 'body',
        name: '内容',
        type: 'string',
        required: true,
        description: '邮件内容',
      },
    ],
    outputs: [
      {
        id: 'success',
        name: '发送状态',
        type: 'boolean',
        required: true,
        description: '是否发送成功',
      },
    ],
  },
];
