<template>
  <el-dialog
    v-model="visible"
    title="导出工作流"
    width="800px"
    @close="handleClose"
  >
    <div class="export-dialog">
      <!-- 导出格式选择 -->
      <div class="format-selection">
        <h4>选择导出格式</h4>
        <el-radio-group v-model="selectedFormat" @change="generateExport">
          <el-radio-button label="json">JSON (通用)</el-radio-button>
          <el-radio-button label="airflow">Apache Airflow</el-radio-button>
          <el-radio-button label="github-actions">GitHub Actions</el-radio-button>
          <el-radio-button label="azure-pipelines">Azure Pipelines</el-radio-button>
          <el-radio-button label="jenkins">Jenkins Pipeline</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 导出选项 -->
      <div class="export-options">
        <h4>导出选项</h4>
        <el-checkbox v-model="includeMetadata">包含元数据</el-checkbox>
        <el-checkbox v-model="includeComments">包含注释</el-checkbox>
        <el-checkbox v-model="minifyOutput">压缩输出</el-checkbox>
      </div>

      <!-- 预览区域 -->
      <div class="preview-section">
        <div class="preview-header">
          <h4>预览</h4>
          <div class="preview-actions">
            <el-button @click="copyToClipboard" size="small">
              <el-icon><DocumentCopy /></el-icon>
              复制
            </el-button>
            <el-button @click="downloadFile" type="primary" size="small">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
          </div>
        </div>
        <el-input
          v-model="exportContent"
          type="textarea"
          :rows="20"
          readonly
          class="preview-content"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleExport" type="primary">导出</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { DocumentCopy, Download } from '@element-plus/icons-vue';
import type { WorkflowDefinition, AirflowDAG, GitHubActionsWorkflow } from '@/types/workflow';

const props = defineProps<{
  modelValue: boolean;
  workflow: WorkflowDefinition;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'export': [format: string, content: string];
}>();

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 导出配置
const selectedFormat = ref('json');
const includeMetadata = ref(true);
const includeComments = ref(true);
const minifyOutput = ref(false);
const exportContent = ref('');

// 监听工作流变化
watch(() => props.workflow, () => {
  if (visible.value) {
    generateExport();
  }
}, { deep: true });

// 生成导出内容
const generateExport = () => {
  switch (selectedFormat.value) {
    case 'json':
      exportContent.value = generateJSON();
      break;
    case 'airflow':
      exportContent.value = generateAirflow();
      break;
    case 'github-actions':
      exportContent.value = generateGitHubActions();
      break;
    case 'azure-pipelines':
      exportContent.value = generateAzurePipelines();
      break;
    case 'jenkins':
      exportContent.value = generateJenkins();
      break;
    default:
      exportContent.value = generateJSON();
  }
};

// 生成JSON格式
const generateJSON = () => {
  const data = includeMetadata.value ? props.workflow : {
    name: props.workflow.name,
    nodes: props.workflow.nodes,
    edges: props.workflow.edges
  };
  
  return minifyOutput.value 
    ? JSON.stringify(data)
    : JSON.stringify(data, null, 2);
};

// 生成Airflow DAG
const generateAirflow = () => {
  const dag: AirflowDAG = {
    dag_id: props.workflow.name.toLowerCase().replace(/\s+/g, '_'),
    description: props.workflow.description || '',
    schedule_interval: '@daily',
    start_date: new Date().toISOString().split('T')[0],
    catchup: false,
    tags: props.workflow.metadata.tags,
    tasks: props.workflow.nodes.map(node => ({
      task_id: node.id,
      operator: getAirflowOperator(node.type),
      params: node.config,
      upstream_task_ids: getUpstreamTasks(node.id)
    }))
  };

  const pythonCode = `
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.operators.bash_operator import BashOperator

default_args = {
    'owner': '${props.workflow.metadata.author}',
    'depends_on_past': False,
    'start_date': datetime(${new Date().getFullYear()}, ${new Date().getMonth() + 1}, ${new Date().getDate()}),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5)
}

dag = DAG(
    '${dag.dag_id}',
    default_args=default_args,
    description='${dag.description}',
    schedule_interval='${dag.schedule_interval}',
    catchup=${dag.catchup}
)

${dag.tasks.map(task => `
${task.task_id} = ${task.operator}(
    task_id='${task.task_id}',
    dag=dag
)`).join('\n')}

# 设置任务依赖
${generateAirflowDependencies()}
`;

  return pythonCode;
};

// 生成GitHub Actions
const generateGitHubActions = () => {
  const workflow: GitHubActionsWorkflow = {
    name: props.workflow.name,
    on: {
      push: {
        branches: ['main']
      },
      workflow_dispatch: {}
    },
    jobs: {}
  };

  // 将节点转换为jobs
  props.workflow.nodes.forEach(node => {
    workflow.jobs[node.id] = {
      'runs-on': 'ubuntu-latest',
      needs: getUpstreamTasks(node.id),
      steps: [
        {
          name: `Checkout code`,
          uses: 'actions/checkout@v3'
        },
        {
          name: node.label,
          run: generateStepCommand(node)
        }
      ]
    };
  });

  return `# ${props.workflow.name}
# Generated from workflow definition

${includeComments.value ? '# This workflow was automatically generated' : ''}

${minifyOutput.value ? JSON.stringify(workflow) : JSON.stringify(workflow, null, 2)}`;
};

// 生成Azure Pipelines
const generateAzurePipelines = () => {
  const pipeline = {
    trigger: ['main'],
    pool: {
      vmImage: 'ubuntu-latest'
    },
    stages: [
      {
        stage: 'Build',
        jobs: props.workflow.nodes.map(node => ({
          job: node.id,
          displayName: node.label,
          dependsOn: getUpstreamTasks(node.id),
          steps: [
            {
              script: generateStepCommand(node),
              displayName: node.label
            }
          ]
        }))
      }
    ]
  };

  return minifyOutput.value 
    ? JSON.stringify(pipeline)
    : JSON.stringify(pipeline, null, 2);
};

// 生成Jenkins Pipeline
const generateJenkins = () => {
  const stages = props.workflow.nodes.map(node => `
        stage('${node.label}') {
            steps {
                script {
                    ${generateStepCommand(node)}
                }
            }
        }`).join('\n');

  return `pipeline {
    agent any
    
    stages {${stages}
    }
    
    post {
        always {
            cleanWs()
        }
    }
}`;
};

// 辅助函数
const getAirflowOperator = (nodeType: string) => {
  const operators = {
    input: 'PythonOperator',
    output: 'PythonOperator',
    process: 'PythonOperator',
    condition: 'BranchPythonOperator'
  };
  return operators[nodeType as keyof typeof operators] || 'PythonOperator';
};

const getUpstreamTasks = (nodeId: string) => {
  return props.workflow.edges
    .filter(edge => edge.target === nodeId)
    .map(edge => edge.source);
};

const generateStepCommand = (node: any) => {
  switch (node.type) {
    case 'input':
      return `echo "Processing input: ${node.label}"`;
    case 'output':
      return `echo "Generating output: ${node.label}"`;
    case 'process':
      return `echo "Processing: ${node.label}"`;
    default:
      return `echo "Executing: ${node.label}"`;
  }
};

const generateAirflowDependencies = () => {
  return props.workflow.edges.map(edge => 
    `${edge.source} >> ${edge.target}`
  ).join('\n');
};

// 操作函数
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(exportContent.value);
    ElMessage.success('已复制到剪贴板');
  } catch (err) {
    ElMessage.error('复制失败');
  }
};

const downloadFile = () => {
  const extensions = {
    json: 'json',
    airflow: 'py',
    'github-actions': 'yml',
    'azure-pipelines': 'yml',
    jenkins: 'groovy'
  };
  
  const extension = extensions[selectedFormat.value as keyof typeof extensions] || 'txt';
  const filename = `${props.workflow.name.toLowerCase().replace(/\s+/g, '_')}.${extension}`;
  
  const blob = new Blob([exportContent.value], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
  URL.revokeObjectURL(url);
};

const handleExport = () => {
  emit('export', selectedFormat.value, exportContent.value);
  handleClose();
};

const handleClose = () => {
  visible.value = false;
};

// 初始化
watch(visible, (newVisible) => {
  if (newVisible) {
    generateExport();
  }
});
</script>

<style scoped>
.export-dialog {
  margin: 0;
  padding: 0;
}

.export-dialog > :not(:first-child) {
  margin-top: 1.5rem;
}

.format-selection h4,
.export-options h4,
.preview-section h4 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.75rem;
}

.export-options {
  margin: 0;
  padding: 0;
}

.export-options > :not(:first-child) {
  margin-top: 0.5rem;
}

.preview-section {
  margin: 0;
  padding: 0;
}

.preview-section > :not(:first-child) {
  margin-top: 0.75rem;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
}

.preview-content {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
}

:deep(.el-textarea__inner) {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}
</style>
